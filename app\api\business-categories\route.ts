import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryIds = searchParams.get('categoryIds')

    if (!categoryIds) {
      return NextResponse.json(
        { error: 'Category IDs are required' },
        { status: 400 }
      )
    }

    // Parse category IDs
    const categoryIdArray = categoryIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))

    if (!categoryIdArray.length) {
      return NextResponse.json(
        { error: 'Valid category IDs are required' },
        { status: 400 }
      )
    }

    // Get businesses that have any of these categories (simplified query)
    const { data: businessCategories, error } = await adminClient
      .from('business_categories')
      .select('business_id')
      .in('category_id', categoryIdArray)

    if (error) {
      console.error('Error fetching business categories:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business categories' },
        { status: 500 }
      )
    }

    // Extract unique business IDs
    const allBusinessIds = [...new Set(businessCategories?.map(bc => bc.business_id) || [])]

    // Now filter to only approved businesses
    if (allBusinessIds.length === 0) {
      return NextResponse.json({
        businessIds: []
      })
    }

    const { data: approvedBusinesses, error: businessError } = await adminClient
      .from('businesses')
      .select('id')
      .in('id', allBusinessIds)
      .eq('is_approved', true)

    if (businessError) {
      console.error('Error fetching approved businesses:', businessError)
      return NextResponse.json(
        { error: 'Failed to fetch approved businesses' },
        { status: 500 }
      )
    }

    // Extract approved business IDs
    const businessIds = approvedBusinesses?.map(b => b.id) || []

    return NextResponse.json({
      businessIds: businessIds
    })
  } catch (error) {
    console.error('Unhandled error in business categories API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
