// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey ? 'Key is set' : 'Key is not set');

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function queryDatabase() {
  try {
    console.log('=== CHECKING BUSINESS CATEGORY SUBSCRIPTIONS ===\n');

    // Check business_categories table
    console.log('1. Checking business_categories table...');
    const { data: businessCategories, error: businessCategoriesError } = await supabase
      .from('business_categories')
      .select('business_id, category_id, is_primary')
      .limit(20);

    if (businessCategoriesError) {
      if (businessCategoriesError.code === '42P01') {
        console.log('❌ business_categories table does NOT exist');
      } else {
        console.error('Error querying business_categories:', businessCategoriesError);
      }
    } else {
      console.log('✅ business_categories table exists');
      console.log(`📊 Found ${businessCategories?.length || 0} business category subscriptions`);

      if (businessCategories && businessCategories.length > 0) {
        console.log('\n🏪 Business Category Subscriptions:');
        businessCategories.forEach(sub => {
          const isPrimary = sub.is_primary ? ' (PRIMARY)' : '';
          console.log(`   • Business ID ${sub.business_id} → Category ID ${sub.category_id}${isPrimary}`);
        });
      } else {
        console.log('❌ No businesses are currently subscribed to any categories');
      }
    }

    console.log('\n2. Checking categories table...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id, name, slug, is_active')
      .eq('is_active', true)
      .limit(10);

    if (categoriesError) {
      console.error('Error querying categories:', categoriesError);
    } else {
      console.log(`✅ Found ${categories?.length || 0} active categories`);
      if (categories && categories.length > 0) {
        console.log('\n📋 Available Categories:');
        categories.forEach(cat => {
          console.log(`   • ${cat.name} (${cat.slug}) - ID: ${cat.id}`);
        });
      }
    }

    console.log('\n3. Checking businesses table...');
    const { data: businesses, error: businessesError } = await supabase
      .from('businesses')
      .select('id, name, slug, is_approved')
      .eq('is_approved', true)
      .limit(10);

    if (businessesError) {
      console.error('Error querying businesses:', businessesError);
    } else {
      console.log(`✅ Found ${businesses?.length || 0} approved businesses`);
      if (businesses && businesses.length > 0) {
        console.log('\n🏪 Approved Businesses:');
        businesses.forEach(biz => {
          console.log(`   • ${biz.name} (${biz.slug}) - ID: ${biz.id}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Database query failed:', error);
  }
}

queryDatabase();
