"use client"

import Link from "next/link"
import { ShoppingBasket, ShoppingBag, Trash2, X, Clock, HelpCircle, LogIn, ChevronDown, ChevronUp, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>itle, SheetClose } from "@/components/ui/sheet"
import { useRealtimeCart } from "@/context/realtime-cart-context"
import { useAuth } from "@/context/unified-auth-context"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useState, useEffect, useCallback } from "react"
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { useRealtimeCheckout } from "@/hooks/use-realtime-checkout"
import { Badge } from "@/components/ui/badge"
import CheckoutLink from "@/components/checkout-link"
import { getUserCoordinates, getUserPostcode } from "@/lib/user-location"
import { calculateDistance, calculateDeliveryFeeWithDistance } from "@/lib/distance-calculation"

// Utility function to format business name from slug or ID
const formatBusinessName = (name: string | number): string => {
  if (!name) return 'Unknown Business';

  // Convert to string if it's a number
  const nameStr = String(name);

  // If it's a numeric ID, return a placeholder
  if (!isNaN(Number(nameStr)) && !nameStr.includes('-')) {
    return `Business ${nameStr}`;
  }

  // If the name doesn't contain hyphens, it's likely already formatted
  if (!nameStr.includes('-')) {
    return nameStr;
  }

  // For "Business jersey Wings" format, fix it to "Jersey Wings"
  if (nameStr.toLowerCase().startsWith('business jersey')) {
    return nameStr.replace(/^business jersey/i, 'Jersey');
  }

  // Split by hyphens, capitalize each word, and join with spaces
  return nameStr
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to format variant and customization details
const formatItemDetails = (item: any): string[] => {
  const details: string[] = [];

  // Add variant information
  if (item.variantName) {
    details.push(`Size: ${item.variantName}`);
  }

  // Add customization information
  if (item.customizations && item.customizations.length > 0) {
    item.customizations.forEach((customization: any) => {
      if (customization.options && customization.options.length > 0) {
        const optionNames = customization.options.map((option: any) => {
          if (option.price > 0) {
            return `${option.name} (+£${option.price.toFixed(2)})`;
          }
          return option.name;
        });
        details.push(`${customization.groupName}: ${optionNames.join(', ')}`);
      }
    });
  }

  // Fallback to legacy options if no variant/customization data
  if (details.length === 0 && item.options && item.options.length > 0) {
    details.push(...item.options);
  }

  return details;
}

export default function CartSheet() {
  const {
    cart,
    totalPrice,
    totalItems,
    removeFromCart,
    updateQuantity,
    clearCart,
    getItemsByBusiness,
    getBusinessNames,
    getDeliveryMethod,
    setDeliveryMethod,
    getDeliveryFee,
    setDeliveryFee,
    deliveryMethods,
    isConnected,
    isInitialized,
    lastSyncTime,
    syncStatus,
    isProcessingOrder
  } = useRealtimeCart()
  const { user } = useAuth()
  const itemsByBusiness = getItemsByBusiness()
  const businessNames = getBusinessNames()

  // Use the real-time checkout hook to get business details and delivery fees
  // Use the real-time checkout hook to get business details and delivery fees
  const {
    businessDetails: realtimeBusinessDetails,
    businessDeliveryFees,
    businessPreparationTimes,
    isLoading: isLoadingBusinessDetails,
    error: businessDetailsError,
    calculateDeliveryFees
  } = useRealtimeCheckout();

  // Create a local state for business details that we can update
  const [businessDetails, setBusinessDetails] = useState<Record<string, any>>(realtimeBusinessDetails || {});

  // Sync businessDetails with realtimeBusinessDetails when it changes
  useEffect(() => {
    if (realtimeBusinessDetails && Object.keys(realtimeBusinessDetails).length > 0) {
      setBusinessDetails(prevDetails => {
        // Merge the realtime details with our local details
        const mergedDetails = { ...prevDetails };

        Object.entries(realtimeBusinessDetails).forEach(([businessId, details]) => {
          if (!mergedDetails[businessId]) {
            // If we don't have this business yet, add it
            mergedDetails[businessId] = details;
          } else {
            // If we already have this business, update its properties
            // but preserve any name we might have set locally
            const name = mergedDetails[businessId].name || (details as any).name;
            mergedDetails[businessId] = {
              ...(details as any),
              name: name
            };
          }
        });

        return mergedDetails;
      });
    }

    // Log the business details for debugging
    console.log("Updated business details:", businessDetails);
  }, [realtimeBusinessDetails]);

  const [showFaqDialog, setShowFaqDialog] = useState(false)
  const [openBusinesses, setOpenBusinesses] = useState<Record<string, boolean>>({})

  // Calculate delivery fee for a business using the same method as the business page
  const calculateDeliveryFee = async (businessId: string | number) => {
    // If we have a delivery fee from the real-time checkout hook, use it
    if (businessDeliveryFees[businessId]?.fee !== undefined) {
      return businessDeliveryFees[businessId].fee;
    }

    // If we have business details but no delivery fee yet, calculate it using the API
    if (businessDetails[businessId]) {
      const business = businessDetails[businessId];

      // Base delivery fee
      const baseFee = typeof business.delivery_fee === 'number' ? business.delivery_fee : 2.50;

      // Get user coordinates and postcode
      const userCoords = getUserCoordinates();
      let userPostcode = getUserPostcode();

      // Try to get postcode from loop_jersey_postcode if not found
      if (!userPostcode && typeof window !== 'undefined') {
        try {
          userPostcode = localStorage.getItem('loop_jersey_postcode');
        } catch (e) {
          console.error("Error accessing localStorage for postcode:", e);
        }
      }

      // If we don't have user coordinates or postcode, or business coordinates, return the base fee
      if ((!userCoords && !userPostcode) || !business.coordinates) {
        return baseFee;
      }

      // Default postcode if not found
      if (!userPostcode) {
        userPostcode = "JE2 3QN"; // Default Jersey postcode (St Helier)
      }

      try {
        // Use the API to calculate the delivery fee for consistency with business page
        const response = await fetch('/api/delivery/calculate-fee', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            businessId,
            businessCoordinates: business.coordinates,
            customerCoordinates: userCoords,
            postcode: userPostcode,
            deliveryFeeModel: business.delivery_fee_model || 'fixed',
            deliveryFee: baseFee,
            deliveryFeePerKm: business.delivery_fee_per_km || 0.50
          })
        });

        if (!response.ok) {
          console.error(`Failed to calculate delivery fee via API: ${response.status}`);

          // Fall back to direct calculation if API fails
          if (userCoords && business.coordinates) {
            // Calculate the actual distance between business and customer
            const distance = calculateDistance(
              business.coordinates[0], business.coordinates[1],
              userCoords[0], userCoords[1]
            );

            console.log(`Fallback: Calculated distance for ${business.name || businessId}: ${distance.toFixed(2)}km`);

            // Calculate the fee based on the model and distance
            const fee = calculateDeliveryFeeWithDistance(
              baseFee,
              business.delivery_fee_model,
              business.delivery_fee_per_km,
              distance
            );

            console.log(`Fallback: Calculated fee for ${business.name || businessId}: £${fee.toFixed(2)} (${business.delivery_fee_model} model, base: £${baseFee.toFixed(2)}, per km: £${business.delivery_fee_per_km?.toFixed(2) || '0.00'}, distance: ${distance.toFixed(2)}km)`);

            return fee;
          }

          return baseFee;
        }

        const data = await response.json();
        console.log(`API calculated fee for ${business.name || businessId}: £${data.fee.toFixed(2)}, distance: ${data.distance.toFixed(2)}km`);

        return data.fee;
      } catch (error) {
        console.error(`Error calculating delivery fee for ${business.name || businessId}:`, error);
        return baseFee;
      }
    }

    // Default delivery fee if we don't have any business details
    return 2.50;
  }

  // Function to explicitly fetch business details and calculate delivery fee
  // Define this outside of the component to avoid recreation
  const fetchAndCalculateDeliveryFeeImpl = async (businessId: string | number): Promise<number> => {
    try {
      // Get user coordinates and postcode from multiple possible sources
      let userCoords = getUserCoordinates();
      let userPostcode = getUserPostcode();

      // If coordinates not found, try the loop_jersey_coordinates key
      if (!userCoords && typeof window !== 'undefined') {
        try {
          const loopCoordsStr = localStorage.getItem('loop_jersey_coordinates');
          if (loopCoordsStr) {
            const loopCoords = JSON.parse(loopCoordsStr);
            if (Array.isArray(loopCoords) && loopCoords.length === 2 &&
                typeof loopCoords[0] === 'number' && typeof loopCoords[1] === 'number') {
              userCoords = loopCoords as [number, number];
              console.log("Found coordinates in loop_jersey_coordinates:", userCoords);
            }
          }
        } catch (e) {
          console.error("Error parsing loop_jersey_coordinates:", e);
        }
      }

      // If postcode not found, try the loop_jersey_postcode key
      if (!userPostcode && typeof window !== 'undefined') {
        userPostcode = localStorage.getItem('loop_jersey_postcode');
        if (userPostcode) {
          console.log("Found postcode in loop_jersey_postcode:", userPostcode);
        }
      }

      // If still no coordinates or postcode, use default Jersey coordinates and postcode
      if (!userCoords) {
        userCoords = [-2.1065, 49.1868]; // Default Jersey coordinates (St Helier)
        console.log("Using default Jersey coordinates:", userCoords);
      }

      if (!userPostcode) {
        userPostcode = "JE2 3QN"; // Default Jersey postcode (St Helier)
        console.log("Using default Jersey postcode:", userPostcode);
      }

      console.log(`Fetching business details for ${businessId} to calculate delivery fee`);

      // Fetch business details from the API
      const response = await fetch(`/api/businesses/${businessId}/details`);

      if (!response.ok) {
        console.error(`Failed to fetch business details for ${businessId}: ${response.status}`);
        return 2.50; // Default fee
      }

      const businessData = await response.json();
      console.log(`Received business details for ${businessId}:`, businessData);

      // Check if we have the necessary data to calculate the fee
      if (!businessData.coordinates) {
        console.error(`Business ${businessId} has no coordinates`);
        return businessData.delivery_fee || 2.50;
      }

      // Parse coordinates if needed
      let businessCoords: [number, number] | null = null;

      if (typeof businessData.coordinates === 'string') {
        try {
          // Try to parse PostgreSQL POINT format: "(longitude,latitude)"
          const pointMatch = businessData.coordinates.match(/\(([-\d.]+),([-\d.]+)\)/);
          if (pointMatch && pointMatch.length === 3) {
            businessCoords = [parseFloat(pointMatch[1]), parseFloat(pointMatch[2])];
          } else {
            // Try other string formats
            const parsedCoords = JSON.parse(businessData.coordinates);
            if (Array.isArray(parsedCoords) && parsedCoords.length === 2) {
              businessCoords = parsedCoords as [number, number];
            }
          }
        } catch (e) {
          console.error(`Error parsing coordinates for ${businessId}:`, e);
        }
      } else if (Array.isArray(businessData.coordinates) && businessData.coordinates.length === 2) {
        businessCoords = businessData.coordinates as [number, number];
      }

      if (!businessCoords) {
        console.error(`Could not parse coordinates for business ${businessId}`);
        return businessData.delivery_fee || 2.50;
      }

      console.log(`Calculating delivery fee for ${businessId} using API`);

      // Calculate the fee using the API
      const feeResponse = await fetch('/api/delivery/calculate-fee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          businessId,
          businessCoordinates: businessCoords,
          customerCoordinates: userCoords,
          postcode: userPostcode,
          deliveryFeeModel: businessData.delivery_fee_model || 'fixed',
          deliveryFee: businessData.delivery_fee || 2.50,
          deliveryFeePerKm: businessData.delivery_fee_per_km || 0.50
        })
      });

      if (!feeResponse.ok) {
        console.error(`Failed to calculate delivery fee for ${businessId}: ${feeResponse.status}`);

        // If API call fails, calculate manually
        const distance = calculateDistance(
          businessCoords[0], businessCoords[1],
          userCoords[0], userCoords[1]
        );

        const fee = calculateDeliveryFeeWithDistance(
          businessData.delivery_fee || 2.50,
          businessData.delivery_fee_model || 'fixed',
          businessData.delivery_fee_per_km || 0.50,
          distance
        );

        console.log(`Manually calculated fee for ${businessId}: £${fee.toFixed(2)}`);
        return fee;
      }

      const feeData = await feeResponse.json();
      console.log(`API calculated fee for ${businessId}: £${feeData.fee.toFixed(2)}, distance: ${feeData.distance.toFixed(2)}km`);

      return feeData.fee;
    } catch (error) {
      console.error(`Error calculating delivery fee for ${businessId}:`, error);
      return 2.50; // Default fee
    }
  };

  // Use the implementation in a stable reference
  const fetchAndCalculateDeliveryFee = useCallback(fetchAndCalculateDeliveryFeeImpl, []);

  // TEMPORARILY DISABLED - Initialize delivery fee calculation when component mounts
  // This useEffect was causing continuous API calls due to setDeliveryFee in dependency array
  // TODO: Re-enable with proper dependency management to prevent infinite loops
  /*
  useEffect(() => {
    let isMounted = true;

    const initializeDeliveryFees = async () => {
      // For each business in the cart with delivery method set to 'delivery',
      // calculate and update the delivery fee only if it hasn't been set yet
      for (const businessId of Object.keys(itemsByBusiness)) {
        // Skip if component unmounted
        if (!isMounted) return;

        // Check if delivery is available for this business
        // Consider delivery unavailable if business details don't exist yet or delivery_available is explicitly false
        const deliveryAvailable = businessDetails[businessId] && businessDetails[businessId].delivery_available !== false;

        console.log(`Initializing delivery method for ${businessId}, delivery available: ${deliveryAvailable}`);

        // Only set to pickup if delivery is not available for this business
        // But NEVER change the delivery method of a business that already has items in the cart
        if (!deliveryAvailable && getDeliveryMethod(businessId) === 'delivery') {
          // Check if this is a newly added business (not previously in the cart)
          const isNewlyAddedBusiness = !deliveryMethods[businessId];

          if (isNewlyAddedBusiness) {
            console.log(`Delivery not available for ${businessId}, setting method to pickup`);
            setDeliveryMethod(businessId, 'pickup');
          } else {
            console.log(`Delivery not available for ${businessId}, but keeping existing delivery method because it's already in the cart`);
          }
          continue;
        }

        // Only calculate delivery fee if delivery is available and method is set to delivery
        if (deliveryAvailable && getDeliveryMethod(businessId) === 'delivery') {
          // Check if we already have a delivery fee for this business
          const currentFee = getDeliveryFee(businessId);

          // Always calculate the fee to ensure it's up-to-date
          console.log(`Initializing delivery fee for ${businessId}`);

          // Use our explicit function to fetch and calculate the delivery fee
          const fee = await fetchAndCalculateDeliveryFee(businessId);

          // Skip if component unmounted
          if (!isMounted) return;

          // Update the delivery fee in the cart context
          console.log(`Setting initial delivery fee for ${businessId} to £${fee.toFixed(2)}`);
          setDeliveryFee(businessId, fee);
        }
      }
    };

    // Run the initialization
    initializeDeliveryFees();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [itemsByBusiness, getDeliveryMethod, getDeliveryFee, setDeliveryFee, businessDetails, deliveryMethods]);
  */

  // Set default values for all businesses immediately
  const businessIds = Object.keys(itemsByBusiness);
  const defaultOpenState: Record<string, boolean> = {};

  // Set default values for all businesses
  for (const businessId of businessIds) {
    defaultOpenState[businessId] = true; // All business sections open by default
  }

  // Initialize state with default values
  if (Object.keys(openBusinesses).length === 0 && businessIds.length > 0) {
    setOpenBusinesses(defaultOpenState);
  }

  // Handle real-time updates to delivery_available field
  useEffect(() => {
    // For each business in the cart, check if delivery is available
    for (const businessId of Object.keys(itemsByBusiness)) {
      // Check if delivery is available for this business
      // Consider delivery unavailable if business details don't exist yet or delivery_available is explicitly false
      const deliveryAvailable = businessDetails[businessId] && businessDetails[businessId].delivery_available !== false;

      // Only switch to pickup if delivery is not available for this business
      // But NEVER change the delivery method of a business that already has items in the cart
      if (!deliveryAvailable && getDeliveryMethod(businessId) === 'delivery') {
        // Check if this is a newly added business (not previously in the cart)
        const isNewlyAddedBusiness = !deliveryMethods[businessId];

        if (isNewlyAddedBusiness) {
          console.log(`Initial setup: Delivery not available for ${businessId}, setting to pickup`);
          setDeliveryMethod(businessId, 'pickup');
        } else {
          console.log(`Initial setup: Delivery not available for ${businessId}, but keeping existing delivery method because it's already in the cart`);
        }
      }
    }
  }, [businessDetails, itemsByBusiness, getDeliveryMethod, setDeliveryMethod, deliveryMethods]);

  // Add debugging to track delivery availability for each business
  useEffect(() => {
    // Only log if we have business details
    if (Object.keys(businessDetails).length > 0) {
      console.log("Business details updated:", businessDetails);

      // Log delivery availability for each business
      Object.keys(itemsByBusiness).forEach(businessId => {
        // Check if we have business details for this business
        if (businessDetails[businessId]) {
          // Log the actual delivery_available value from the database
          console.log(`Business ${businessId} delivery_available from DB: ${businessDetails[businessId].delivery_available}`);
        } else {
          console.log(`Business ${businessId} details not loaded yet`);
        }

        // Check if delivery is available for this business
        const available = businessDetails[businessId] && businessDetails[businessId].delivery_available !== false;
        console.log(`Business ${businessId} delivery available: ${available}, current method: ${getDeliveryMethod(businessId)}`);
      });
    }
  }, [businessDetails, itemsByBusiness, getDeliveryMethod]);

  // Add a useEffect to fetch business names for any business IDs that don't have names yet
  useEffect(() => {
    const fetchMissingBusinessNames = async () => {
      // Get all business IDs that don't have names in businessDetails
      const missingBusinessIds = Object.keys(itemsByBusiness).filter(
        businessId => !businessDetails[businessId]?.name
      );

      if (missingBusinessIds.length === 0) return;

      console.log(`Fetching missing business names for: ${missingBusinessIds.join(', ')}`);

      try {
        // First try to fetch all business names at once using a direct database query
        try {
          const response = await fetch('/api/businesses/names-by-ids', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ businessIds: missingBusinessIds })
          });

          if (response.ok) {
            const data = await response.json();
            console.log('Fetched business names in bulk:', data);

            if (data.businesses && data.businesses.length > 0) {
              // Update businessDetails with the fetched names
              const updatedDetails = { ...businessDetails };

              data.businesses.forEach((business: any) => {
                if (business && business.id && business.name) {
                  const businessId = business.id.toString();

                  if (!updatedDetails[businessId]) {
                    updatedDetails[businessId] = {
                      id: businessId,
                      name: business.name,
                      coordinates: null,
                      delivery_fee: business.delivery_fee || 2.50,
                      delivery_fee_model: business.delivery_fee_model || 'fixed',
                      delivery_fee_per_km: business.delivery_fee_per_km || 0.50,
                      preparation_time_minutes: business.preparation_time_minutes || 15,
                      opening_hours: null,
                      delivery_available: business.delivery_available !== false
                    };
                  } else {
                    updatedDetails[businessId].name = business.name;
                  }

                  console.log(`Updated business name for ${businessId}: ${business.name}`);
                }
              });

              setBusinessDetails(updatedDetails);
              return; // Exit early if bulk fetch succeeded
            }
          }
        } catch (bulkError) {
          console.error("Error fetching business names in bulk:", bulkError);
          // Continue to individual fetches as fallback
        }

        // Fallback: Fetch business names individually in parallel
        const promises = missingBusinessIds.map(async (businessId) => {
          try {
            const response = await fetch(`/api/businesses/${businessId}/details`);
            if (!response.ok) {
              console.error(`Failed to fetch business details for ${businessId}: ${response.status}`);
              return null;
            }

            const data = await response.json();
            return { businessId, name: data.name };
          } catch (error) {
            console.error(`Error fetching business details for ${businessId}:`, error);
            return null;
          }
        });

        const results = await Promise.all(promises);

        // Update businessDetails with the fetched names
        const updatedDetails = { ...businessDetails };

        results.forEach(result => {
          if (result && result.name) {
            if (!updatedDetails[result.businessId]) {
              updatedDetails[result.businessId] = {
                id: result.businessId,
                name: result.name,
                coordinates: null,
                delivery_fee: 2.50,
                delivery_fee_model: 'fixed',
                delivery_fee_per_km: 0.50,
                preparation_time_minutes: 15,
                opening_hours: null,
                delivery_available: true
              };
            } else {
              updatedDetails[result.businessId].name = result.name;
            }

            console.log(`Updated business name for ${result.businessId}: ${result.name}`);
          }
        });

        setBusinessDetails(updatedDetails);
      } catch (error) {
        console.error("Error fetching missing business names:", error);
      }
    };

    fetchMissingBusinessNames();
  }, [itemsByBusiness, businessDetails]);

  // Effect to automatically calculate delivery fees when business details are loaded
  // This runs quickly after business details are fetched to ensure delivery fees are calculated
  useEffect(() => {
    const calculateMissingDeliveryFees = async () => {
      // For each business in the cart
      for (const businessId of Object.keys(itemsByBusiness)) {
        // Ensure we have items for this business
        const businessItems = itemsByBusiness[businessId];
        if (!businessItems || businessItems.length === 0) {
          continue; // Skip if no items for this business
        }

        // Check if we have business details and delivery is available
        const businessDetail = businessDetails[businessId];
        if (!businessDetail || businessDetail.delivery_available === false) {
          continue; // Skip if no details or delivery not available
        }

        // Check if delivery method is set to delivery
        const deliveryMethod = getDeliveryMethod(parseInt(businessId, 10));
        if (deliveryMethod !== 'delivery') {
          continue; // Skip if not delivery method
        }

        // Check if delivery fee is missing (0 or undefined)
        const currentFee = getDeliveryFee(parseInt(businessId, 10));
        if (currentFee > 0) {
          continue; // Skip if fee already calculated
        }

        // Calculate delivery fee automatically
        try {
          const fee = await fetchAndCalculateDeliveryFee(businessId);
          await setDeliveryFee(parseInt(businessId, 10), fee);
        } catch (error) {
          // Silently handle errors to avoid console spam
        }
      }
    };

    // Only run if we have business details loaded and items in cart
    if (Object.keys(businessDetails).length > 0 && Object.keys(itemsByBusiness).length > 0) {
      calculateMissingDeliveryFees();
    }
  }, [businessDetails, itemsByBusiness, getDeliveryMethod, getDeliveryFee, setDeliveryFee, fetchAndCalculateDeliveryFee]);

  // Function to toggle a business section's open/closed state
  const toggleBusinessSection = (businessId: string) => {
    setOpenBusinesses(prev => ({
      ...prev,
      [businessId]: !prev[businessId]
    }));
  }

  // Log business names for debugging
  useEffect(() => {
    // Always call hooks in the same order, regardless of cart state
    // This ensures React's rules of hooks are followed

    // Only log if cart has items
    if (cart.length > 0) {
      console.log("Cart items by business:", itemsByBusiness);
      console.log("Business names from context:", businessNames);
      console.log("Business details:", businessDetails);

      // Log each business name resolution
      Object.keys(itemsByBusiness).forEach(businessId => {
        const dbName = businessDetails[businessId]?.name;
        const contextName = businessNames[businessId];
        const formattedName = formatBusinessName(contextName || businessId);
        const finalName = dbName || formattedName;

        console.log(`Business name for ${businessId}:`, {
          dbName,
          contextName,
          formattedName,
          finalName
        });
      });
    } else {
      console.log("Cart is empty");
    }
  }, [cart, itemsByBusiness, businessNames, businessDetails]);

  if (cart.length === 0) {
    // Show loading screen if order is being processed
    if (isProcessingOrder) {
      return (
        <div className="flex flex-col h-full">
          <SheetHeader className="px-4 py-6 border-b">
            <div className="flex items-center justify-between">
              <SheetTitle>Your Cart</SheetTitle>
              <SheetClose asChild>
                <Button variant="ghost" size="icon">
                  <X className="h-4 w-4" />
                </Button>
              </SheetClose>
            </div>
          </SheetHeader>

          <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
            <div className="w-16 h-16 rounded-full bg-emerald-50 flex items-center justify-center mb-4">
              <Loader2 className="h-8 w-8 text-emerald-600 animate-spin" />
            </div>
            <h3 className="font-medium text-lg mb-2 text-emerald-700">Processing your order...</h3>
            <p className="text-gray-500 mb-6">Please wait while we confirm your order</p>
          </div>
        </div>
      )
    }

    // Show empty cart screen
    return (
      <div className="flex flex-col h-full">
        <SheetHeader className="px-4 py-6 border-b">
          <div className="flex items-center justify-between">
            <SheetTitle>Your Cart</SheetTitle>
            <SheetClose asChild>
              <Button variant="ghost" size="icon">
                <X className="h-4 w-4" />
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>

        <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
          <div className="w-16 h-16 rounded-full bg-emerald-50 flex items-center justify-center mb-4">
            <ShoppingBasket className="h-8 w-8 text-emerald-600" />
          </div>
          <h3 className="font-medium text-lg mb-2 text-emerald-700">Your cart is empty</h3>
          <p className="text-gray-500 mb-6">Add items to your cart to continue</p>
          <SheetClose asChild>
            <Link href="/">
              <Button className="bg-emerald-600 hover:bg-emerald-700">Browse Services</Button>
            </Link>
          </SheetClose>
        </div>
      </div>
    )
  }

  // Calculate total delivery fee from cart context
  const totalDeliveryFee = Object.keys(itemsByBusiness).reduce((total, businessId) => {
    // Only include delivery fees for businesses that:
    // 1. Have delivery method set to 'delivery'
    // 2. Actually offer delivery (delivery_available !== false)
    const deliveryMethod = getDeliveryMethod(parseInt(businessId, 10));
    const businessDetail = businessDetails[businessId];
    const deliveryAvailable = businessDetail && businessDetail.delivery_available !== false;

    if (deliveryMethod === 'delivery' && deliveryAvailable) {
      const fee = getDeliveryFee(parseInt(businessId, 10));
      return total + fee;
    }
    return total;
  }, 0);
  // Service fee should come from a configuration or API, but for now we'll keep it as is
  const serviceFee = 0.5
  const grandTotal = totalPrice + totalDeliveryFee + serviceFee

  return (
    <div className="flex flex-col h-full relative">
      {/* Real-time Status Indicator */}
      {!isInitialized && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-3">
            <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
            <div className="text-center">
              <p className="text-sm font-medium text-gray-900">Initializing real-time cart...</p>
              <p className="text-xs text-gray-500">Setting up live synchronization</p>
              <p className="text-xs text-gray-400 mt-2">
                Status: {syncStatus} | Connected: {isConnected ? 'Yes' : 'No'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Connection Status */}
      {isInitialized && !isConnected && (
        <div className="px-4 py-2 bg-yellow-50 border-b border-yellow-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-yellow-700">Reconnecting...</span>
            </div>
            {lastSyncTime && (
              <span className="text-xs text-yellow-600">
                Last sync: {lastSyncTime.toLocaleTimeString()}
              </span>
            )}
          </div>
        </div>
      )}



      <SheetHeader className="px-6 py-6 border-b bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <ShoppingBag className="mr-3 text-emerald-600 h-6 w-6" />
            <div>
              <SheetTitle className="text-xl font-semibold">Your Cart</SheetTitle>
              <p className="text-sm text-gray-600 mt-1">{totalItems} {totalItems === 1 ? 'item' : 'items'}</p>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 ml-3"
                    onClick={() => setShowFaqDialog(true)}
                  >
                    <HelpCircle className="h-4 w-4 text-gray-400" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>View FAQs</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200"
              onClick={clearCart}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Clear Cart
            </Button>
            <SheetClose asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <X className="h-4 w-4" />
              </Button>
            </SheetClose>
          </div>
        </div>
      </SheetHeader>

      <div className="flex-1 overflow-y-auto py-6">
        <div className="px-6 space-y-6">
          {Object.entries(itemsByBusiness).map(([businessId, items]) => {
            // Calculate business subtotal for the header
            const businessSubtotal = items.reduce((total, item) => {
              return total + item.price * item.quantity
            }, 0);

            return (
              <div key={businessId} className="bg-white rounded-lg shadow-md border border-gray-100">
                {/* Business Header */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-center mb-4">
                    <ShoppingBag className="mr-2 text-emerald-600" />
                    <h3 className="text-lg font-semibold">
                      {businessDetails[businessId]?.name || formatBusinessName(businessNames[businessId] || businessId)}
                    </h3>
                  </div>

                  {/* Business Info */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center gap-4">
                      {items[0]?.businessType && (
                        <span>{items[0].businessType.charAt(0).toUpperCase() + items[0].businessType.slice(1)}</span>
                      )}
                      {businessPreparationTimes[businessId] && (
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>Prep: {businessPreparationTimes[businessId]} min</span>
                        </div>
                      )}
                    </div>
                    <span className="font-semibold text-emerald-600">£{businessSubtotal.toFixed(2)}</span>
                  </div>

                  {/* Delivery Method Selection */}
                  <div className="mt-4">
                    <span className="text-sm font-medium text-gray-700 block mb-2">Delivery Method:</span>
                    {(() => {
                      const hasDetails = !!businessDetails[businessId];
                      const deliveryAvailableValue = businessDetails[businessId]?.delivery_available;

                      return (!hasDetails || deliveryAvailableValue === false) ? (
                        <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200">
                          Pickup Only
                        </Badge>
                      ) : (
                        <div className="flex space-x-2">
                          <Button
                            variant={getDeliveryMethod(parseInt(businessId, 10)) === 'delivery' ? 'default' : 'outline'}
                            size="sm"
                            className={getDeliveryMethod(parseInt(businessId, 10)) === 'delivery'
                              ? 'bg-blue-600 hover:bg-blue-700'
                              : 'hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200'
                            }
                            onClick={async () => {
                              if (!businessDetails[businessId] || businessDetails[businessId]?.delivery_available === false) {
                                return;
                              }
                              setDeliveryMethod(parseInt(businessId, 10), 'delivery');
                              const fee = await fetchAndCalculateDeliveryFee(businessId);
                              await setDeliveryFee(parseInt(businessId, 10), fee);
                            }}
                          >
                            Delivery
                          </Button>
                          <Button
                            variant={getDeliveryMethod(parseInt(businessId, 10)) === 'pickup' ? 'default' : 'outline'}
                            size="sm"
                            className={getDeliveryMethod(parseInt(businessId, 10)) === 'pickup'
                              ? 'bg-orange-600 hover:bg-orange-700'
                              : 'hover:bg-orange-50 hover:text-orange-600 hover:border-orange-200'
                            }
                            onClick={() => {
                              setDeliveryMethod(parseInt(businessId, 10), 'pickup');
                            }}
                          >
                            Pickup
                          </Button>
                        </div>
                      );
                    })()}
                  </div>
                </div>

                {/* Items List */}
                <div className="p-6">
                  <div className="space-y-4">
                    {items.map((item) => {
                      const itemDetails = formatItemDetails(item);
                      return (
                        <div key={item.cartItemId || `${item.id}-${Math.random()}`} className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <p className="font-medium text-gray-900">{item.quantity}x {item.name}</p>
                                {itemDetails.length > 0 && (
                                  <p className="text-sm text-gray-500 mt-1">{itemDetails.join(", ")}</p>
                                )}
                              </div>
                              <p className="font-medium text-gray-900">£{(item.price * item.quantity).toFixed(2)}</p>
                            </div>

                            {/* Quantity Controls */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  className="h-8 w-8 rounded-full"
                                  onClick={() => updateQuantity(item.id, item.quantity - 1, item.variantId?.toString())}
                                  disabled={item.quantity <= 1}
                                >
                                  <span>-</span>
                                </Button>
                                <span className="w-8 text-center text-sm font-medium">{item.quantity}</span>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  className="h-8 w-8 rounded-full"
                                  onClick={() => updateQuantity(item.id, item.quantity + 1, item.variantId?.toString())}
                                >
                                  <span>+</span>
                                </Button>
                              </div>

                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-500 hover:text-red-600 hover:bg-red-50"
                                onClick={() => removeFromCart(item.id, item.variantId?.toString())}
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Remove
                              </Button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Business Total */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal</span>
                      <span>£{businessSubtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Delivery Fee</span>
                      <span>
                        {getDeliveryMethod(parseInt(businessId, 10)) === 'delivery'
                          ? (() => {
                              const currentFee = getDeliveryFee(parseInt(businessId, 10));
                              if (businessDeliveryFees[businessId]?.fee !== undefined && currentFee === 0) {
                                const calculatedFee = businessDeliveryFees[businessId].fee;
                                return calculatedFee === 0 ? "Free" : `£${calculatedFee.toFixed(2)}`;
                              }
                              return currentFee === 0 ? "Free" : `£${currentFee.toFixed(2)}`;
                            })()
                          : "Free"}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Service Fee</span>
                      <span>£0.50</span>
                    </div>
                    <div className="flex justify-between font-semibold pt-2 border-t border-gray-200">
                      <span>{businessDetails[businessId]?.name || formatBusinessName(businessNames[businessId] || businessId)} Total</span>
                      <span>£{(businessSubtotal + (getDeliveryMethod(parseInt(businessId, 10)) === 'delivery' ? getDeliveryFee(parseInt(businessId, 10)) : 0) + 0.5).toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer with Grand Total and Checkout */}
      <div className="border-t bg-white p-6">
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Order Subtotal</span>
              <span>£{totalPrice.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Total Delivery Fees</span>
              <span>£{totalDeliveryFee.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Service Fee</span>
              <span>£{serviceFee.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg pt-3 border-t border-gray-200">
              <span>Grand Total</span>
              <span className="text-emerald-600">£{grandTotal.toFixed(2)}</span>
            </div>
          </div>
        </div>

        <SheetClose asChild>
          <CheckoutLink
            buttonClassName="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-3 rounded-lg shadow-sm"
          />
        </SheetClose>
      </div>

      {/* FAQ Dialog */}
      <Dialog open={showFaqDialog} onOpenChange={setShowFaqDialog}>
        <DialogContent className="max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">Frequently Asked Questions</DialogTitle>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">About Loop Jersey</h3>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">What is Loop Jersey?</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Loop Jersey is on a mission to transform the way you order food, drinks, and essentials across Jersey.
                    We partner with the best local restaurants, cafes, and shops – from neighbourhood favourites to popular
                    island brands – and bring what you love, straight to your door.
                  </p>
                  <p className="text-sm text-gray-600 mt-2">
                    With a growing choice of partners and a team of dedicated delivery riders, we'll have your order with
                    you quickly and reliably.
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">What is the story behind Loop Jersey?</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Loop Jersey was launched to provide a better, faster, and more local delivery service for Jersey residents.
                    Founded by a team of local entrepreneurs, Loop was created to support island businesses and deliver a
                    premium experience to customers.
                  </p>
                  <p className="text-sm text-gray-600 mt-2">
                    We're proud to be based entirely in Jersey and passionate about keeping it local!
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">Using Loop Jersey</h3>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">How does it work?</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    You can order either on the website or by using the Loop Jersey app, available on iOS and Android.
                    Enter your postcode (or use your location) to find local restaurants, cafes, and stores delivering
                    in your area. Choose your items, place your order, and relax while we deliver to your door.
                  </p>
                  <p className="text-sm text-gray-600 mt-2">
                    You can even schedule an order in advance to suit your plans.
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">What kind of restaurants and stores are listed on Loop Jersey?</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    We carefully select a high-quality and diverse range of restaurants, cafes, and retailers across Jersey.
                    Whether you're craving a gourmet burger, fresh seafood, artisan coffee, or essentials from a local shop,
                    you'll find it on Loop Jersey.
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">Fees on Loop Jersey</h3>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">How do fees work?</h4>
                  <div className="text-sm text-gray-600 mt-1 space-y-2">
                    <p className="font-medium text-gray-700">Delivery fee</p>
                    <p>The delivery fee is based on your location and will be shown clearly before you place your order.</p>

                    <p className="font-medium text-gray-700">Service fee</p>
                    <p>This helps us power the Loop Jersey experience, maintain the app, and provide customer support. It's calculated based on your order total.</p>

                    <p className="font-medium text-gray-700">Small order fee</p>
                    <p>If your basket total is below a certain minimum, we may apply a small order fee. You can avoid this by adding more items to your basket.</p>

                    <p className="font-medium text-gray-700">Extended delivery fee</p>
                    <p>For deliveries from partners located further from you, a small extended delivery fee may apply. This will be shown before you confirm your order.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <Link href="/faqs" className="text-emerald-600 hover:underline font-medium">
                View all FAQs
              </Link>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
